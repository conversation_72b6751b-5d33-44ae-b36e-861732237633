<div class="p-4" style="background-color: #f9f9f9cc; border-radius: 12px;">
  <div class="d-flex justify-content-between align-items-center mb-4 border-bottom pb-2">
    <div>
      <h4 class="fw-bold m-0">Job Posts</h4>
      <p class="text-muted mb-0">Manage job postings and opportunities</p>
    </div>
    <div class="d-flex gap-2 align-items-center">
      <select
        class="form-select"
        style="width: auto;"
        [(ngModel)]="pageSize"
        (change)="onPageSizeChange()">
        <option value="2">2 per page</option>
        <option value="6">6 per page</option>
        <option value="9">9 per page</option>
        <option value="12">12 per page</option>
      </select>
      <button
        class="btn add-btn"
        data-bs-toggle="modal"
        data-bs-target="#jobModal"
        (click)="openModal('create')">
        Create Job
      </button>
    </div>
  </div>

  <!-- List of Posted Jobs -->
  <div class="mt-4" *ngIf="jobs.length > 0; else noJobs">
    <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4 job-post-grid">
      <div class="col" *ngFor="let job of jobs; let i = index">
        <div class="card rounded h-100 border-0">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center border-bottom pb-2 mb-3 flex-wrap gap-2">
            <h6 class="card-title fw-bold m-0">{{ job.jobTitle }}</h6>

              <div class="button-group">
              <button
                class="btn edit-btn"
                data-bs-toggle="modal"
                data-bs-target="#jobModal"
                (click)="openModal('edit', job)">
                <i class="bi bi-pencil-square"></i>
              </button>
              <button
                class="btn delete-btn"
                (click)="openDeleteModal(i, job.jobId, job.jobTitle)">
                <i class="bi bi-trash"></i>
              </button>
            </div>
            </div>
            <ul class="list-unstyled mb-3 job-info">
              <li><i class="fa-solid fa-clock"></i> <span><strong>Type:</strong> {{ job.jobType }}</span></li>
              <li><i class="fa-solid fa-laptop-code"></i> <span><strong>Mode:</strong> {{ job.mode }}</span></li>
              <li *ngIf="job.location"><i class="fa-solid fa-map-pin"></i> <span><strong>Location:</strong> {{ job.location }}</span></li>
              <li><i class="fa-solid fa-sack-dollar"></i> <span><strong>Salary:</strong> ₹{{ job.salaryPackage }}</span></li>
              <li class="require-full" *ngIf="job.requirements"><i class="fa-solid fa-file-alt"></i> <span><strong>Requirements:</strong> {{ job.requirements }}</span></li>
            </ul>
            <p class="card-text mb-2">{{ job.description }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <ng-template #noJobs>
    <div class="text-center py-5">
      <i class="bi bi-briefcase display-1 text-muted"></i>
      <h4 class="mt-3">No Jobs Posted</h4>
      <p class="text-muted">No jobs have been posted yet. Click <strong>"Create Job"</strong> to add one.</p>
    </div>
  </ng-template>

  <!-- Pagination -->
  <nav *ngIf="totalPages > 1" aria-label="Job posts pagination" class="mt-4">
    <ul class="pagination justify-content-center">
      <li class="page-item" [class.disabled]="pageNumber === 1">
        <button class="page-link" (click)="prevPage()" [disabled]="pageNumber === 1">
          <i class="bi bi-chevron-left"></i>
          Previous
        </button>
      </li>

      <li *ngFor="let page of getPaginationArray()"
          class="page-item"
          [class.active]="page === pageNumber">
        <button class="page-link" (click)="goToPage(page)">
          {{ page }}
        </button>
      </li>

      <li class="page-item" [class.disabled]="pageNumber === totalPages">
        <button class="page-link" (click)="nextPage()" [disabled]="pageNumber === totalPages">
          Next
          <i class="bi bi-chevron-right"></i>
        </button>
      </li>
    </ul>
  </nav>

  <!-- Pagination Info -->
  <div *ngIf="totalCount > 0" class="text-center mt-3">
    <small class="text-muted">
      Showing {{ getStartIndex() }} to {{ getEndIndex() }} of {{ totalCount }} job posts
    </small>
  </div>
</div>

<!-- Create/Edit Job Modal -->
<div
  class="modal fade"
  id="jobModal"
  tabindex="-1"
  aria-labelledby="jobModalLabel"
  aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content shadow">
      <div class="modal-header  text-white">
        <h6 class="modal-title" id="jobModalLabel">
          {{ modalMode === 'create' ? 'Create New Job' : 'Edit Job' }}
        </h6>
        <button
          type="button"
          class="btn-close btn-close-white"
          data-bs-dismiss="modal"
          aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form
          (ngSubmit)="modalMode === 'create' ? postJob() : updateJob()"
          class="row g-3"
          #jobForm="ngForm">
          <div class="col-md-6">
            <label class="form-label fw-semibold">Job Title</label>
            <input
              type="text"
              class="form-control"
              [(ngModel)]="job.jobTitle"
              name="jobTitle"
              required />
          </div>

          <div class="col-md-6">
            <label class="form-label fw-semibold">Company</label>
            <input
              type="text"
              class="form-control"
              [(ngModel)]="job.company"
              name="company"
              required />
          </div>

          <div class="col-md-6">
            <label class="form-label fw-semibold">Job Type</label>
            <input
              type="text"
              class="form-control"
              [(ngModel)]="job.jobType"
              name="jobType"
              required />
          </div>

          <div class="col-md-6">
            <label class="form-label fw-semibold">Mode</label>
            <input
              type="text"
              class="form-control"
              [(ngModel)]="job.mode"
              name="mode"
              required />
          </div>

          <div class="col-md-6">
            <label class="form-label fw-semibold">Location</label>
            <input
              type="text"
              class="form-control"
              [(ngModel)]="job.location"
              name="location"
              required />
          </div>

          <div class="col-md-6">
            <label class="form-label fw-semibold">Salary</label>
            <input
              type="number"
              class="form-control"
              [(ngModel)]="job.salaryPackage"
              name="salaryPackage"
              required />
          </div>

          <div class="col-12">
            <label class="form-label fw-semibold">Requirements</label>
            <input
              type="text"
              class="form-control"
              [(ngModel)]="job.requirements"
              name="requirements"
              required />
          </div>

          <div class="col-12">
            <label class="form-label fw-semibold">Description</label>
            <textarea
              class="form-control"
              rows="4"
              [(ngModel)]="job.description"
              name="description"
              required></textarea>
          </div>

          <div class="d-flex justify-content-end mt-4 gap-2 pt-3 border-top">
            <button
              type="button"
              class="btn btn-secondary ms-2"
              (click)="resetForm()">
              Reset
            </button>
            <button
              type="submit"
              class="btn post-btn"
              [disabled]="!jobForm.form.valid">
              {{ modalMode === 'create' ? 'Post Job' : 'Update Job' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h6 class="modal-title text-white" id="deleteConfirmModalLabel">Confirm Delete</h6>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p>Are you sure you want to delete the job <strong>"{{ jobToDelete?.title }}"</strong>?</p>
        <p class="text-muted">This action cannot be undone.</p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button
          type="button"
          class="btn btn-danger"
          (click)="confirmDelete()"
          [disabled]="isDeleting">
          <span *ngIf="isDeleting" class="spinner-border spinner-border-sm me-2" role="status"></span>
          {{ isDeleting ? 'Deleting...' : 'Delete Job' }}
        </button>
      </div>
    </div>
  </div>
</div>